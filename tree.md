# Project Structure

```
.
├── .next/                      # Next.js build output (generated)
├── docs.md                     # Project documentation
├── tree.md                     # THIS FILE
├── eslint.config.mjs           # ESLint configuration
├── next-env.d.ts               # TypeScript declarations for Next.js
├── next.config.ts              # Next.js configuration
├── node_modules/               # Project dependencies
├── package.json                # Project manifest
├── package-lock.json           # Dependency lock file
├── postcss.config.mjs          # PostCSS configuration
├── public/                     # Static assets (currently empty)
├── README.md                   # Project README
├── src/                        # Source code
│   ├── app/
│   │   ├── admin/
│   │   │   └── page.tsx
│   │   ├── api/
│   │   │   ├── articles/
│   │   │   │   ├── [id]/
│   │   │   │   │   └── route.ts
│   │   │   │   └── route.ts
│   │   │   ├── resources/
│   │   │   │   └── route.ts
│   │   │   └── workshops/
│   │   │       └── route.ts
│   │   ├── resource/
│   │   │   └── [id]/
│   │   │       └── page.tsx
│   │   ├── workshop/
│   │   │   └── [id]/
│   │   │       └── page.tsx
│   │   ├── globals.css
│   │   ├── layout.tsx
│   │   └── page.tsx
│   ├── components/
│   │   ├── admin/
│   │   │   ├── AdminDashboard.tsx
│   │   │   ├── AdminLoginForm.tsx
│   │   │   ├── ArticleEditor.tsx
│   │   │   ├── ArticlesTable.tsx
│   │   │   └── WorkshopsTable.tsx
│   │   ├── home/
│   │   │   ├── ResourceList.tsx
│   │   │   ├── ResourceTabs.tsx
│   │   │   ├── ResourceView.tsx
│   │   │   └── SearchInput.tsx
│   │   ├── layout/
│   │   │   └── main-layout.tsx
│   │   ├── markdown-preview.tsx
│   │   └── ui/                   # ShadCN UI components
│   │       ├── button.tsx
│   │       ├── card.tsx
│   │       ├── command.tsx
│   │       ├── dialog.tsx
│   │       ├── input.tsx
│   │       ├── label.tsx
│   │       ├── popover.tsx
│   │       ├── table.tsx
│   │       ├── tabs.tsx
│   │       └── textarea.tsx
│   ├── db/
│   │   └── schema.sql
│   ├── lib/
│   │   ├── db.ts
│   │   └── utils.ts
│   └── store/
│       ├── adminStore.ts
│       └── data-store.ts
└── tsconfig.json               # TypeScript configuration
``` 