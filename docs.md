# WorkshopKnowledgeBase (WKB)

## Project Objective

The WorkshopKnowledgeBase (WKB) is a Single Page Application (SPA) designed to provide easy access to a variety of articles and resources for internal use at a workplace. The goal is to create a centralized knowledge hub where users can find and consume content posted by an administrator.

### Key Features

*   **Search Functionality**: Allows users to search for articles across the entire knowledge base.
*   **Category/Tag Organization**: Content will be organized with categories and/or tags for easy browsing and filtering.
*   **Rich Text Editor**: An administrator will use a rich text editor to create and format articles.
*   **File Uploads**: The application will support uploading and associating files (e.g., PDFs, images) with articles.
*   **Simple Admin Panel**: A straightforward admin interface for adding, editing, and managing content.

## Technologies

The project is built using the following technologies:

*   **Next.js**: A React framework for building full-stack web applications. It will be used for both the frontend (React) and the backend API routes.
*   **React**: A JavaScript library for building user interfaces.
*   **SQLite**: A lightweight, serverless, self-contained SQL database engine that is great for small to medium-sized applications. It will be used to store article content, metadata, and other application data. We are using the `better-sqlite3` package for this.
*   **Tailwind CSS**: A utility-first CSS framework for rapidly building custom designs.
*   **TypeScript**: A typed superset of JavaScript that compiles to plain JavaScript.
*   **Zustand**: A small, fast, and scalable bearbones state-management solution.
*   **Zod**: A TypeScript-first schema declaration and validation library.
*   **React Markdown**: A library to render Markdown as React components. We also use `remark-gfm` for tables, strikethrough, etc.
*   **Framer Motion**: A production-ready motion library for React.
*   **Lucide React**: A library of simply beautiful icons.

## UI Components

This project uses [shadcn/ui](https://ui.shadcn.com/) for UI components. It is a collection of reusable components that you can copy and paste into your apps.

### Adding New Components

To add a new component, run the following command from the root of the project:

```bash
npx shadcn-ui@latest add [component-name]
```

For example, to add a button component, you would run:

```bash
npx shadcn-ui@latest add button
```

This will add the component's source code to the `src/components/ui` directory. You can then import it into your files like this:

```tsx
import { Button } from "@/components/ui/button";
```

## Development Dependencies

The following are some of the key development dependencies:

*   **ESLint**: A tool for identifying and reporting on patterns found in ECMAScript/JavaScript code.
*   **tsx**: A CLI that allows you to run TypeScript and ESM files directly. 