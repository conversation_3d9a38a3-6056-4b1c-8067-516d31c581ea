import Database from "better-sqlite3";
import fs from "fs";
import path from "path";

declare global {
  // allow global `var` declarations
  // eslint-disable-next-line no-var
  var __db: Database.Database | undefined;
}

function initializeDb() {
  const dbPath = path.resolve(process.cwd(), "wkb.db");
  const dbExists = fs.existsSync(dbPath);
  const db = new Database(dbPath);

  if (!dbExists) {
    const schemaPath = path.resolve(process.cwd(), "src/db/schema.sql");
    const schema = fs.readFileSync(schemaPath, "utf-8");
    db.exec(schema);
    console.log("Database initialized and schema applied.");
  } else {
    console.log("Connected to existing database.");
  }

  return db;
}

let db: Database.Database;

if (process.env.NODE_ENV === "production") {
  db = initializeDb();
} else {
  if (!global.__db) {
    global.__db = initializeDb();
  }
  db = global.__db;
}

export default db; 