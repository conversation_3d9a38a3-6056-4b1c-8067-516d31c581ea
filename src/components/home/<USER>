"use client";

import { Input } from "@/components/ui/input";

export function SearchInput({
  searchQuery,
  setSearchQuery,
}: {
  searchQuery: string;
  setSearchQuery: (query: string) => void;
}) {
  return (
    <div className="w-full max-w-2xl mb-8">
      <Input
        type="search"
        placeholder="Search for resources..."
        className="w-full transition-all duration-300 focus:shadow-lg focus:ring-2 focus:ring-blue-500"
        value={searchQuery}
        onChange={(e) => setSearchQuery(e.target.value)}
      />
    </div>
  );
} 