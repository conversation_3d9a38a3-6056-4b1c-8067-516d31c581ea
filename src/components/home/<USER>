"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { AnimatePresence, motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { ResourceList } from "./ResourceList";
import { Resource, Workshop } from "@/types";

export function ResourceTabs({
  activeTab,
  filteredResources,
}: {
  activeTab: string;
  filteredResources: (Resource | Workshop)[];
}) {
  const router = useRouter();

  const handleTabChange = (value: string) => {
    router.push(`/?tab=${value}`);
  };

  return (
    <Tabs
      value={activeTab}
      className="w-full max-w-4xl"
      onValueChange={handleTabChange}
    >
      <TabsList>
        <TabsTrigger value="workshop">Workshop</TabsTrigger>
        <TabsTrigger value="all">All Resources</TabsTrigger>
      </TabsList>
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          <TabsContent value={activeTab} asChild>
            <ResourceList
              resources={filteredResources}
              type={activeTab as "workshop" | "all"}
            />
          </TabsContent>
        </motion.div>
      </AnimatePresence>
    </Tabs>
  );
} 