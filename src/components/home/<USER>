"use client";

import { useDataStore } from "@/store/data-store";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { SearchInput } from "./SearchInput";
import { ResourceTabs } from "./ResourceTabs";
import { Resource, Workshop } from "@/types";

export function ResourceView() {
  const searchParams = useSearchParams();
  const [searchQuery, setSearchQuery] = useState("");
  const { workshops, resources, setWorkshops, setResources } = useDataStore();

  useEffect(() => {
    const fetchWorkshops = async () => {
      const response = await fetch("/api/workshops");
      const data = await response.json();
      setWorkshops(data);
    };

    const fetchResources = async () => {
      const response = await fetch("/api/resources");
      const data = await response.json();
      setResources(data);
    };

    fetchWorkshops();
    fetchResources();
  }, [setWorkshops, setResources]);

  const activeTab = searchParams.get("tab") || "workshop";

  const [filteredResources, setFilteredResources] = useState<(Resource | Workshop)[]>([]);

  useEffect(() => {
    const sourceData = activeTab === "workshop" ? workshops : resources;
    if (sourceData) {
      const filtered = sourceData.filter((resource) =>
        resource.title.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredResources(filtered);
    }
  }, [searchQuery, activeTab, workshops, resources]);

  return (
    <>
      <SearchInput searchQuery={searchQuery} setSearchQuery={setSearchQuery} />
      <ResourceTabs
        activeTab={activeTab}
        filteredResources={filteredResources}
      />
    </>
  );
} 