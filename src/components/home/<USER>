import Link from "next/link";
import { Resource, Workshop } from "@/types";

export function ResourceList({
  resources,
  type,
}: {
  resources: (Resource | Workshop)[];
  type: "workshop" | "all";
}) {
  if (resources.length === 0) {
    return (
      <div className="text-center text-gray-500 animate-fade-in mt-8">
        <p>No resources found.</p>
      </div>
    );
  }

  return (
    <ul className="space-y-4 mt-8">
      {resources.map((resource) => (
        <Link
          key={resource.id}
          href={`/${type === "workshop" ? "workshop" : "resource"}/${
            resource.id
          }`}
        >
          <li className="p-4 border rounded-lg hover:bg-muted transition-transform duration-200 ease-in-out hover:scale-[1.03]">
            <h3 className="text-xl font-semibold">{resource.title}</h3>
            <p className="text-sm text-gray-500 mb-2">
              {new Date(resource.createdAt).toLocaleDateString()}
            </p>
            <p>{resource.description}</p>
          </li>
        </Link>
      ))}
    </ul>
  );
} 