"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

export default function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isAdminPage = pathname === "/admin";

  return (
    <>
      {!isAdminPage && (
        <header className="py-4 px-6 border-b">
          <div className="container mx-auto flex justify-center items-center">
            <Link
              href="/"
              className="transition-transform duration-300 hover:scale-105"
            >
              <h1 className="text-2xl font-bold">
                <span className="text-blue-500">W</span>orkshop
                <span className="text-blue-500">K</span>nowledge
                <span className="text-blue-500">B</span>ase
              </h1>
            </Link>
          </div>
        </header>
      )}
      <main
        className={
          isAdminPage ? "" : "flex-1 flex flex-col items-center p-6"
        }
      >
        {children}
      </main>
    </>
  );
} 