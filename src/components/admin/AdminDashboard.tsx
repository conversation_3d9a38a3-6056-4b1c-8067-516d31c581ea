"use client";

import { useEffect } from "react";
import { motion } from "framer-motion";
import { Input } from "@/components/ui/input";
import { Toaster } from "sonner";
import { useAdminStore } from "@/store/adminStore";
import { ArticleEditor } from "./ArticleEditor";
import { ArticlesTable } from "./ArticlesTable";
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { WorkshopsTable } from "./WorkshopsTable";

export function AdminDashboard() {
  const { fetchArticles, fetchWorkshops, searchQuery, setSearchQuery } = useAdminStore();

  useEffect(() => {
    fetchArticles();
    fetchWorkshops();
  }, [fetchArticles, fetchWorkshops]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="flex flex-col items-center min-h-screen bg-background p-8"
    >
      <Toaster richColors />
      <motion.div
        className="bg-card p-8 rounded-lg shadow-lg w-full max-w-6xl"
        initial={{ scale: 0.9 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-4">
            <h1 className="text-4xl font-bold">Admin Dashboard</h1>
            <Input
              placeholder="Search by title..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="max-w-sm"
            />
          </div>
          <ArticleEditor />
        </div>
        <Tabs defaultValue="articles" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="articles">Articles</TabsTrigger>
            <TabsTrigger value="workshops">Workshops</TabsTrigger>
          </TabsList>
          <TabsContent value="articles">
            <ArticlesTable />
          </TabsContent>
          <TabsContent value="workshops">
            <WorkshopsTable />
          </TabsContent>
        </Tabs>
      </motion.div>
    </motion.div>
  );
} 