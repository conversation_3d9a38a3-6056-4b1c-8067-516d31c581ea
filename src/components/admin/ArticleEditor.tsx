"use client";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { ChevronsUpDown, Check, Loader2, X } from "lucide-react";
import { useAdminStore } from "@/store/adminStore";
import { cn, truncateText } from "@/lib/utils";

export function ArticleEditor() {
  const {
    open,
    setOpen,
    creationStep,
    handleOpenCreateModal,
    isEditing,
    itemType,
    handleSubmit,
    title,
    setTitle,
    description,
    setDescription,
    popoverOpen,
    setPopoverOpen,
    selectedWorkshop,
    setSelectedWorkshop,
    workshops,
    isSubmitting,
  } = useAdminStore();

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button>Create New...</Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px]">
        {creationStep === "selectType" && (
          <>
            <DialogHeader>
              <DialogTitle>What would you like to create?</DialogTitle>
              <DialogDescription>
                Select whether you want to create a new stand-alone Workshop or
                an Article that belongs to a workshop.
              </DialogDescription>
            </DialogHeader>
            <div className="flex justify-center gap-4 py-4">
              <Button
                variant="outline"
                onClick={() => handleOpenCreateModal("workshop")}
              >
                New Workshop
              </Button>
              <Button onClick={() => handleOpenCreateModal("article")}>
                New Article
              </Button>
            </div>
          </>
        )}
        {creationStep === "form" && (
          <>
            <DialogHeader>
              <DialogTitle>
                {isEditing ? `Edit ${itemType}` : `Create new ${itemType}`}
              </DialogTitle>
              <DialogDescription>
                Fill in the details below. You can use Markdown for the content.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-2 gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    required
                  />
                  {itemType === "article" && (
                    <div className="space-y-2">
                      <Label>Workshop</Label>
                      <div className="flex items-center gap-2">
                        <div className="flex-1">
                          <Popover
                            open={popoverOpen}
                            onOpenChange={setPopoverOpen}
                          >
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                role="combobox"
                                aria-expanded={popoverOpen}
                                className="w-full justify-between"
                              >
                                <span className="truncate">
                                  {selectedWorkshop
                                    ? truncateText(selectedWorkshop.title, 30)
                                    : "Select workshop..."}
                                </span>
                                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-[375px] p-0 bg-background">
                              <Command>
                                <CommandInput placeholder="Search workshop..." />
                                <CommandList>
                                  <CommandEmpty>No workshop found.</CommandEmpty>
                                  <CommandGroup>
                                    {workshops.map((workshop) => (
                                      <CommandItem
                                        key={workshop.id}
                                        value={workshop.title}
                                        onSelect={(currentValue) => {
                                          const selected = workshops.find(
                                            (w) =>
                                              w.title.toLowerCase() ===
                                              currentValue.toLowerCase()
                                          );
                                          setSelectedWorkshop(selected || null);
                                          setPopoverOpen(false);
                                        }}
                                      >
                                        <Check
                                          className={cn(
                                            "mr-2 h-4 w-4",
                                            selectedWorkshop?.id ===
                                              workshop.id
                                              ? "opacity-100"
                                              : "opacity-0"
                                          )}
                                        />
                                        <span className="truncate">{truncateText(workshop.title, 40)}</span>
                                      </CommandItem>
                                    ))}
                                  </CommandGroup>
                                </CommandList>
                              </Command>
                            </PopoverContent>
                          </Popover>
                        </div>
                        {selectedWorkshop && (
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => setSelectedWorkshop(null)}
                            className="shrink-0"
                          >
                            <X className="h-4 w-4" />
                            <span className="sr-only">
                              Remove selected workshop
                            </span>
                          </Button>
                        )}
                      </div>
                    </div>
                  )}
                  <Label htmlFor="content">Content (Markdown)</Label>
                  <Textarea
                    id="content"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    required
                    className="h-64"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Preview</Label>
                  <div className="prose dark:prose-invert border rounded-md p-4 h-64 overflow-y-auto">
                    <ReactMarkdown remarkPlugins={[remarkGfm]}>
                      {description}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isEditing ? "Saving..." : "Creating..."}
                    </>
                  ) : isEditing ? (
                    "Save Changes"
                  ) : (
                    "Create"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
} 