"use client";

import { useMemo } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Edit, Trash2 } from "lucide-react";
import { useAdminStore } from "@/store/adminStore";

export function WorkshopsTable() {
  const { workshops, searchQuery, handleOpenEditModal, handleDelete } =
    useAdminStore();

  const filteredWorkshops = useMemo(() => {
    return workshops.filter((workshop) =>
      workshop.title.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [workshops, searchQuery]);

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-[100px]">ID</TableHead>
          <TableHead>Title</TableHead>
          <TableHead>Created At</TableHead>
          <TableHead>Updated At</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {filteredWorkshops.map((workshop) => (
          <TableRow key={workshop.id}>
            <TableCell className="font-medium">{workshop.id}</TableCell>
            <TableCell>{workshop.title}</TableCell>
            <TableCell>
              {new Date(workshop.createdAt).toLocaleString()}
            </TableCell>
            <TableCell>
              {new Date(workshop.updatedAt).toLocaleString()}
            </TableCell>
            <TableCell className="text-right">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleOpenEditModal(workshop)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleDelete(workshop.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
} 