"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Input } from "@/components/ui/input";

interface AdminLoginFormProps {
    onLogin: (password: string) => void;
    error: boolean;
}

export function AdminLoginForm({ onLogin, error }: AdminLoginFormProps) {
  const [password, setPassword] = useState("");

  const handleLogin = () => {
      onLogin(password);
      setPassword("");
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-background">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-sm"
      >
        <Input
          type="password"
          placeholder="Password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          onKeyDown={(e) => e.key === "Enter" && handleLogin()}
          className={`${error ? "border-red-500" : ""}`}
        />
        {error && (
          <p className="text-sm text-red-500 mt-2 text-center">
            Invalid password.
          </p>
        )}
      </motion.div>
    </div>
  );
} 