import { create } from "zustand";
import { toast } from "sonner";
import { Resource, Workshop } from "@/types";

type AdminArticle = Resource | Workshop;

interface AdminState {
  articles: Resource[];
  workshops: Workshop[];
  searchQuery: string;
  open: boolean;
  popoverOpen: boolean;
  creationStep: "selectType" | "form";
  itemType: "article" | "workshop";
  isEditing: boolean;
  currentArticle: AdminArticle | null;
  title: string;
  description: string;
  selectedWorkshop: Workshop | null;
  isSubmitting: boolean;

  fetchArticles: () => Promise<void>;
  fetchWorkshops: () => Promise<void>;
  setSearchQuery: (query: string) => void;
  setOpen: (open: boolean) => void;
  setPopoverOpen: (open: boolean) => void;
  handleOpenCreateModal: (type: "article" | "workshop") => void;
  handleOpenEditModal: (article: AdminArticle) => void;
  handleDelete: (id: number) => Promise<void>;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  resetModalState: () => void;

  // Form field setters
  setTitle: (title: string) => void;
  setDescription: (description: string) => void;
  setSelectedWorkshop: (workshop: Workshop | null) => void;
}

export const useAdminStore = create<AdminState>((set, get) => ({
  articles: [],
  workshops: [],
  searchQuery: "",
  open: false,
  popoverOpen: false,
  creationStep: "selectType",
  itemType: "workshop",
  isEditing: false,
  currentArticle: null,
  title: "",
  description: "",
  selectedWorkshop: null,
  isSubmitting: false,

  fetchArticles: async () => {
    try {
      const response = await fetch("/api/articles?type=article");
      if (response.ok) {
        const data = await response.json();
        set({ articles: data });
      }
    } catch (error) {
      console.error("Failed to fetch articles", error);
    }
  },

  fetchWorkshops: async () => {
    try {
      const response = await fetch("/api/articles?type=workshop");
      if (response.ok) {
        const data = await response.json();
        set({ workshops: data });
      }
    } catch (error) {
      console.error("Failed to fetch workshops", error);
    }
  },
  
  setSearchQuery: (query) => set({ searchQuery: query }),
  
  setOpen: (open) => {
      set({ open });
      if(!open) {
          setTimeout(() => {
              get().resetModalState();
          }, 300);
      }
  },

  setPopoverOpen: (open) => set({ popoverOpen: open }),

  resetModalState: () => {
    set({
      creationStep: "selectType",
      isEditing: false,
      currentArticle: null,
      title: "",
      description: "",
      selectedWorkshop: null,
    });
  },

  handleOpenCreateModal: (type) => {
    set({
      creationStep: "form",
      itemType: type,
      isEditing: false,
      currentArticle: null,
      title: "",
      description: "",
      selectedWorkshop: null,
    });
  },

  handleOpenEditModal: (article) => {
    const workshops = get().workshops;
    let parentWorkshop: Workshop | null = null;
    const isResource = 'workshop_id' in article;
    if(isResource && article.workshop_id){
        parentWorkshop = workshops.find(w => w.id === article.workshop_id) || null;
    }

    set({
      open: true,
      creationStep: "form",
      itemType: isResource ? 'article' : 'workshop',
      isEditing: true,
      currentArticle: article,
      title: article.title,
      description: article.description,
      selectedWorkshop: parentWorkshop
    });
  },

  handleDelete: async (id) => {
    if (window.confirm("Are you sure you want to delete this item?")) {
      try {
        const response = await fetch(`/api/articles/${id}`, {
          method: "DELETE",
        });
        if (response.ok) {
          get().fetchArticles();
          get().fetchWorkshops();
          toast.success("Item deleted successfully!");
        } else {
          toast.error("Failed to delete item.");
        }
      } catch (error) {
        console.error("Failed to delete item", error);
        toast.error("An error occurred while deleting the item.");
      }
    }
  },

  handleSubmit: async (e) => {
    e.preventDefault();
    set({ isSubmitting: true });
    const { isEditing, currentArticle, title, description, itemType, selectedWorkshop } = get();

    const url = isEditing
      ? `/api/articles/${currentArticle?.id}`
      : "/api/articles";
    const method = isEditing ? "PUT" : "POST";

    const body = {
      title,
      content: description,
      type: itemType,
      workshop_id: itemType === 'article' ? selectedWorkshop?.id : null,
    };

    try {
      const response = await fetch(url, {
        method: method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(body),
      });

      if (response.ok) {
        get().fetchArticles();
        if (itemType === 'workshop') {
            get().fetchWorkshops();
        }
        toast.success(
          `'${title}' ${isEditing ? "updated" : "created"} successfully!`
        );
        set({open: false});
      } else {
        const error = await response.json();
        toast.error(`Failed to ${isEditing ? "update" : "create"} '${title}'. Error: ${error}`);
      }
    } catch (error) {
      toast.error(
        `An error occurred while ${
          isEditing ? "updating" : "creating"
        } '${title}' Error: ${error}`
      );
    } finally {
      set({ isSubmitting: false });
    }
  },

  // Form field setters
  setTitle: (title) => set({ title }),
  setDescription: (description) => set({ description }),
  setSelectedWorkshop: (workshop) => set({ selectedWorkshop: workshop }),
})); 