import { create } from 'zustand';
import { Workshop, Resource } from '@/types';

interface DataState {
  workshops: Workshop[];
  resources: Resource[];
  setWorkshops: (workshops: Workshop[]) => void;
  setResources: (resources: Resource[]) => void;
}

export const useDataStore = create<DataState>((set) => ({
  workshops: [],
  resources: [],
  setWorkshops: (workshops) => set({ workshops }),
  setResources: (resources) => set({ resources }),
})); 