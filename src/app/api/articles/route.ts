import { NextResponse } from 'next/server';
import db from '@/lib/db';
import { NextRequest } from 'next/server';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type');

  try {
    if (type === 'workshop') {
      const workshops = await db.prepare('SELECT * FROM workshops').all();
      return NextResponse.json(workshops);
    } else if (type === 'article') {
      const resources = await db.prepare('SELECT * FROM resources').all();
      return NextResponse.json(resources);
    } else {
      // If no type specified, return both
      const workshops = await db.prepare('SELECT * FROM workshops').all();
      const resources = await db.prepare('SELECT * FROM resources').all();
      return NextResponse.json([...workshops, ...resources]);
    }
  } catch (error) {
    console.error('Failed to fetch articles:', error);
    return NextResponse.json(
      { error: 'Failed to fetch articles' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { title, content: description, type, workshop_id } = await request.json();

    if (type === 'workshop') {
      const stmt = db.prepare(
        'INSERT INTO workshops (title, description) VALUES (?, ?)'
      );
      const result = stmt.run(title, description);
      return NextResponse.json({ 
        message: 'Workshop created successfully',
        id: result.lastInsertRowid 
      });
    } else {
      const stmt = db.prepare(
        'INSERT INTO resources (title, description, workshop_id) VALUES (?, ?, ?)'
      );
      const result = stmt.run(title, description, workshop_id);
      return NextResponse.json({ 
        message: 'Resource created successfully',
        id: result.lastInsertRowid 
      });
    }
  } catch (error) {
    console.error('Failed to create article:', error);
    return NextResponse.json(
      { error: 'Failed to create article' },
      { status: 500 }
    );
  }
}
