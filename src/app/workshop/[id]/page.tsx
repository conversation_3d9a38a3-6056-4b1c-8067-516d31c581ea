"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useDataStore } from "@/store/data-store";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { MarkdownPreview } from "@/components/markdown-preview";

export default function WorkshopPage() {
  const params = useParams();
  const router = useRouter();
  const workshopId = params.id as string;
  const { workshops, resources } = useDataStore();

  const workshop = workshops.find((w) => w.id.toString() === workshopId);
  const associatedResources = resources.filter(
    (r) => r.workshop_id?.toString() === workshopId
  );

  if (!workshop) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <p>Workshop not found or still loading...</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl">
      <Button variant="outline" onClick={() => router.back()} className="mb-8">
        &larr; Back
      </Button>
      <header className="mb-8">
        <h1 className="text-4xl font-bold mb-2">{workshop.title}</h1>
        <p className="text-lg text-gray-500">{new Date(workshop.createdAt).toLocaleDateString()}</p>
      </header>
      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4">Description</h2>
        <MarkdownPreview content={workshop.description} />
      </section>
      <section>
        <h2 className="text-2xl font-semibold mb-4">Associated Resources</h2>
        {associatedResources.length > 0 ? (
          <ul className="space-y-4">
            {associatedResources.map((resource) => (
              <Link key={resource.id} href={`/resource/${resource.id}`}>
                <li className="p-4 border rounded-lg hover:bg-muted transition-transform duration-200 ease-in-out hover:scale-[1.03]">
                  <h3 className="text-xl font-semibold">{resource.title}</h3>
                  <p className="text-sm text-gray-500 mb-2">
                    {new Date(resource.createdAt).toLocaleDateString()}
                  </p>
                  <p>{resource.description}</p>
                </li>
              </Link>
            ))}
          </ul>
        ) : (
          <p>No resources associated with this workshop.</p>
        )}
      </section>
    </div>
  );
} 