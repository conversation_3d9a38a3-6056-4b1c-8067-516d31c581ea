"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useDataStore } from "@/store/data-store";
import { useParams, useRouter } from "next/navigation";
import { MarkdownPreview } from "@/components/markdown-preview";

export default function ResourcePage() {
  const params = useParams();
  const router = useRouter();
  const resourceId = params.id as string;
  const { resources } = useDataStore();

  const resource = resources.find((r) => r.id.toString() === resourceId);

  if (!resource) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <p>Resource not found or still loading...</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-4xl">
      <Button variant="outline" onClick={() => router.back()} className="mb-8">
        &larr; Back
      </Button>
      <header className="mb-8">
        <h1 className="text-4xl font-bold mb-2">{resource.title}</h1>
        <p className="text-lg text-gray-500">{new Date(resource.createdAt).toLocaleDateString()}</p>
      </header>
      <article>
        <MarkdownPreview content={resource.description} />
      </article>
    </div>
  );
} 